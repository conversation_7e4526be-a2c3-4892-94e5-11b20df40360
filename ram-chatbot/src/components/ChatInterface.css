.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background-color: #ffffff;
}

.chat-header {
  border-bottom: 1px solid #e5e5e5;
  padding: 1rem 2rem;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.header-icon {
  color: #6b7280;
}

.chat-header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.message {
  display: flex;
  gap: 0.75rem;
  max-width: 100%;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.message.user .message-avatar {
  background-color: #374151;
  color: #ffffff;
}

.message.assistant .message-avatar {
  background-color: #9ca3af;
  color: #ffffff;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message.user .message-content {
  display: flex;
  justify-content: flex-end;
}

.message-text {
  background-color: #f8f9fa;
  border: 1px solid #e5e5e5;
  border-radius: 1rem;
  padding: 0.75rem 1rem;
  line-height: 1.5;
  color: #374151;
  word-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
}

.message.user .message-text {
  background-color: #374151;
  color: #ffffff;
  border-color: #374151;
}

.message.assistant .message-text {
  background-color: #ffffff;
  border-color: #e5e5e5;
  color: #374151;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border: 1px solid #e5e5e5;
  border-radius: 1rem;
  width: fit-content;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-input-container {
  border-top: 1px solid #e5e5e5;
  padding: 1rem 2rem;
  background-color: #ffffff;
}

.chat-form {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 1.5rem;
  padding: 0.75rem 1rem;
  transition: border-color 0.2s;
}

.input-wrapper:focus-within {
  border-color: #9ca3af;
  box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.1);
}

.chat-input {
  flex: 1;
  resize: none;
  background: transparent;
  color: #374151;
  font-size: 1rem;
  line-height: 1.5;
  min-height: 24px;
  max-height: 200px;
  overflow-y: auto;
}

.chat-input::placeholder {
  color: #9ca3af;
}

.send-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e5e5e5;
  color: #9ca3af;
  transition: all 0.2s;
  flex-shrink: 0;
}

.send-button.active {
  background-color: #374151;
  color: #ffffff;
}

.send-button:hover.active {
  background-color: #1f2937;
}

.send-button:disabled {
  cursor: not-allowed;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .chat-header {
    padding: 1rem;
  }

  .chat-messages {
    padding: 0.75rem;
    gap: 1rem;
  }

  .chat-input-container {
    padding: 0.75rem;
  }

  .message-text {
    padding: 0.625rem 0.875rem;
  }

  .header-content {
    max-width: none;
  }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  .chat-interface {
    background-color: #1a1a1a;
  }

  .chat-header {
    background-color: #1a1a1a;
    border-bottom-color: #404040;
  }

  .chat-header h1 {
    color: #e5e5e5;
  }

  .header-icon {
    color: #9ca3af;
  }

  .message.assistant .message-text {
    background-color: #2a2a2a;
    border-color: #404040;
    color: #e5e5e5;
  }

  .message.assistant .message-avatar {
    background-color: #6b7280;
  }

  .typing-indicator {
    background-color: #2a2a2a;
    border-color: #404040;
  }

  .chat-input-container {
    background-color: #1a1a1a;
    border-top-color: #404040;
  }

  .input-wrapper {
    background-color: #2a2a2a;
    border-color: #4b5563;
  }

  .input-wrapper:focus-within {
    border-color: #6b7280;
    box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.1);
  }

  .chat-input {
    color: #e5e5e5;
  }

  .chat-input::placeholder {
    color: #9ca3af;
  }
}
