import { useState, useRef, useEffect } from 'react'
import { Send, Bot, User } from 'lucide-react'
import OpenAI from 'openai'
import './ChatInterface.css'

const ChatInterface = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      role: 'assistant',
      content: 'Hello! I\'m your Revenue Accelerator Model chatbot. How can I help you with revenue acceleration and portfolio profitability analysis today?'
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef(null)
  const textareaRef = useRef(null)

  // Initialize OpenAI client
  const openai = new OpenAI({
    apiKey: import.meta.env.VITE_OPENAI_API_KEY,
    dangerouslyAllowBrowser: true
  })

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!inputValue.trim() || isLoading) return

    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: inputValue.trim()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      const completion = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: `You are an expert RAM Framework consultant and analyst. You have comprehensive knowledge of the Revenue Accelerator Model (RAM) Framework for portfolio profitability analysis.

## Core Framework Knowledge:

### 5 Key Revenue Levers:
1. **Pricing**: Price optimization and increase strategies
2. **Promotions**: Trade investment and promotional effectiveness
3. **Assortment**: SKU rationalization and portfolio optimization
4. **Supply Chain**: Service level optimization and COGS improvement
5. **New Product Pipeline**: Innovation and launch strategies

### 2x2 Strategic Matrix (NTS vs GP%):
- **Green Quadrant**: High Performance (NTS>Hurdle, GP%>Hurdle) → Sustain/Drive Revenue with New Launches
- **Purple Quadrant**: High Profitability, Low Efficiency (NTS<Hurdle, GP%>Hurdle) → Increase Promotion Effectiveness & Distribution
- **Orange Quadrant**: High Efficiency, Low Profitability (NTS>Hurdle, GP%<Hurdle) → Price Increase/COGS Improvement
- **Red Quadrant**: Low Performance (NTS<Hurdle, GP%<Hurdle) → Assortment Optimization/Discontinuations

### Key Metrics:
- **Finance**: NTS, Market Share, GP% Standard, Contribution Margin
- **POS**: Sufficiency, Reach, HCP Endorsement, Media ROI
- **Shipments**: Share of TDP, e-share of search, % Hand Eye Level
- **Consumption**: Freshness Index

### 3-Step Implementation:
1. **Assess**: Current maturity evaluation and gap analysis
2. **Plan**: Cross-functional action planning and business integration
3. **Track**: Dashboard-driven monitoring and course correction

### Organizational Levels:
- **Strategic**: Global/Regional CEO & CFO (high-level decisions)
- **Tactical**: Market MD (market-specific strategies)
- **Operational**: Function/RAM Lead (execution and analytics)

You can help with portfolio analysis, strategic recommendations, metric interpretation, action planning, and performance tracking. Provide specific, actionable insights based on RAM Framework principles.`
          },
          ...messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          {
            role: 'user',
            content: userMessage.content
          }
        ],
        max_tokens: 1000,
        temperature: 0.7
      })

      const assistantMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: completion.choices[0].message.content
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error calling OpenAI API:', error)
      const errorMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please make sure your OpenAI API key is configured correctly.'
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px'
    }
  }

  useEffect(() => {
    adjustTextareaHeight()
  }, [inputValue])

  return (
    <div className="chat-interface">
      <div className="chat-header">
        <div className="header-content">
          <Bot className="header-icon" />
          <h1>Revenue Accelerator Model Chatbot</h1>
        </div>
      </div>

      <div className="chat-messages">
        {messages.map((message) => (
          <div key={message.id} className={`message ${message.role}`}>
            <div className="message-avatar">
              {message.role === 'user' ? (
                <User size={20} />
              ) : (
                <Bot size={20} />
              )}
            </div>
            <div className="message-content">
              <div className="message-text">
                {message.content}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="message assistant">
            <div className="message-avatar">
              <Bot size={20} />
            </div>
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      <div className="chat-input-container">
        <form onSubmit={handleSubmit} className="chat-form">
          <div className="input-wrapper">
            <textarea
              ref={textareaRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Message RAM Assistant..."
              className="chat-input"
              rows={1}
              disabled={isLoading}
            />
            <button
              type="submit"
              className={`send-button ${inputValue.trim() && !isLoading ? 'active' : ''}`}
              disabled={!inputValue.trim() || isLoading}
            >
              <Send size={20} />
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ChatInterface
