# RAM Framework Chatbot

A modern React-based chatbot interface integrated with OpenAI's GPT-4o-mini model, specifically designed for the Revenue Accelerator Model (RAM) Framework. The interface mimics ChatGPT's clean, light theme design.

## Features

- 🤖 **OpenAI GPT-4o-mini Integration**: Powered by the latest OpenAI model
- 💬 **ChatGPT-like Interface**: Clean, modern design with light theme
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile
- ⚡ **Real-time Chat**: Instant responses with typing indicators
- 🎯 **RAM Framework Specialized**: Pre-configured with RAM Framework knowledge
- 🔒 **Secure**: Client-side API key configuration

## RAM Framework Context

The chatbot is specialized in the Revenue Accelerator Model (RAM) Framework, which analyzes portfolio profitability through 5 key levers:

1. **Pricing**
2. **Promotions**
3. **Assortment**
4. **Supply Chain**
5. **New Product Pipeline**

### 2x2 Strategic Matrix

The framework uses a strategic matrix with four quadrants:
- 🟢 **Green**: Sustain/Drive More Revenue with New Launches (NTS>Hurdle, GP%>Hurdle)
- 🟣 **Purple**: Increase Promotion Effectiveness & Distribution (NTS<Hurdle, GP%>Hurdle)
- 🟠 **Orange**: Price Increase/COGS Improvement (NTS>Hurdle, GP%<Hurdle)
- 🔴 **Red**: Assortment Optimization/Discontinuations (NTS<Hurdle, GP%<Hurdle)

## Setup Instructions

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- OpenAI API key

### Installation

1. **Clone and navigate to the project:**
   ```bash
   cd ram-chatbot
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure OpenAI API Key:**
   ```bash
   # Copy the environment template
   cp .env.example .env

   # Edit .env and add your OpenAI API key
   # VITE_OPENAI_API_KEY=your_actual_api_key_here
   ```

4. **Get your OpenAI API Key:**
   - Visit [OpenAI Platform](https://platform.openai.com/api-keys)
   - Create a new API key
   - Copy the key to your `.env` file

5. **Start the development server:**
   ```bash
   npm run dev
   ```

6. **Open your browser:**
   - Navigate to `http://localhost:5173`
   - Start chatting with the RAM Framework assistant!

## Usage

1. **Ask RAM Framework Questions**: The chatbot is pre-trained on RAM Framework concepts
2. **Portfolio Analysis**: Get insights on profitability analysis and strategic positioning
3. **Strategic Guidance**: Receive recommendations based on the 2x2 matrix quadrants
4. **Interactive Chat**: Natural conversation flow with context awareness

## Example Questions

- "How do I analyze my product portfolio using the RAM framework?"
- "What does the green quadrant in the 2x2 matrix represent?"
- "How can I improve promotion effectiveness for my products?"
- "What metrics should I track for assortment optimization?"

## Technology Stack

- **Frontend**: React 18 + Vite
- **Styling**: CSS3 with modern design patterns
- **Icons**: Lucide React
- **AI**: OpenAI GPT-4o-mini
- **Build Tool**: Vite for fast development and building

## Project Structure

```
src/
├── components/
│   ├── ChatInterface.jsx    # Main chat component
│   └── ChatInterface.css    # Chat styling
├── App.jsx                  # Root component
├── App.css                  # App-level styles
├── index.css               # Global styles
└── main.jsx                # Entry point
```

## Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `VITE_OPENAI_API_KEY` | Your OpenAI API key | Yes |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
